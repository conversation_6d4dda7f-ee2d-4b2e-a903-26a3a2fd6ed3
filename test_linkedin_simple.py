#!/usr/bin/env python3
"""
Simple LinkedIn Integration Test
Tests the simplified LinkedIn API without verbose error handling
"""

import requests
import json
from datetime import datetime

# API base URL
BASE_URL = "http://127.0.0.1:8000"

def test_linkedin_inmail():
    """Test LinkedIn InMail functionality"""
    print("🧪 Testing LinkedIn InMail...")
    
    url = f"{BASE_URL}/api/linkedin/send-inmail"
    data = {
        "recipient_id": "demilade-adebanjo-554774202",
        "subject": "Test InMail",
        "message_body": "Hello! This is a test InMail message."
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ InMail sent successfully!")
            print(f"Method: {result.get('result', {}).get('method', 'unknown')}")
            print(f"Timestamp: {result.get('timestamp', 'N/A')}")
        else:
            print("❌ InMail failed")
            try:
                error_detail = response.json()
                print(f"Error: {error_detail.get('detail', 'Unknown error')}")
            except:
                print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print()

def test_linkedin_connection():
    """Test LinkedIn connection request functionality"""
    print("🧪 Testing LinkedIn Connection Request...")
    
    url = f"{BASE_URL}/api/linkedin/send-connection-message"
    data = {
        "recipient_id": "demilade-adebanjo-554774202",
        "message": "Hello! I'd like to connect with you on LinkedIn."
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Connection request sent successfully!")
            print(f"Method: {result.get('result', {}).get('method', 'unknown')}")
            print(f"Timestamp: {result.get('timestamp', 'N/A')}")
        else:
            print("❌ Connection request failed")
            try:
                error_detail = response.json()
                print(f"Error: {error_detail.get('detail', 'Unknown error')}")
            except:
                print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print()

def test_linkedin_status():
    """Test LinkedIn connection status"""
    print("🧪 Testing LinkedIn Status...")
    
    url = f"{BASE_URL}/api/linkedin/status"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Status retrieved successfully!")
            status = result.get('status', {})
            
            # Unipile status
            unipile = status.get('unipile', {})
            print(f"Unipile Available: {unipile.get('available', False)}")
            print(f"Unipile Connected: {unipile.get('connected', False)}")
            print(f"LinkedIn Accounts: {len(unipile.get('accounts', []))}")
            
            # LinkedIn API status
            linkedin_api = status.get('linkedin_api', {})
            print(f"LinkedIn API Available: {linkedin_api.get('available', False)}")
            print(f"LinkedIn API Connected: {linkedin_api.get('connected', False)}")
        else:
            print("❌ Status check failed")
            try:
                error_detail = response.json()
                print(f"Error: {error_detail.get('detail', 'Unknown error')}")
            except:
                print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print()

def main():
    """Main test function"""
    print("LinkedIn Integration Simple Test")
    print(f"Time: {datetime.now().isoformat()}")
    print("=" * 50)
    print()
    
    # Test status first
    test_linkedin_status()
    
    # Test InMail
    test_linkedin_inmail()
    
    # Test connection request
    test_linkedin_connection()
    
    print("=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
