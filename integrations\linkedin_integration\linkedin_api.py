"""
LinkedIn Messaging API Integration
Handles authentication and messaging for LinkedIn Business/Personal accounts
Uses Unipile API as primary method with LinkedIn API for content management
Focus on InMail, connection messaging, and company page messaging
"""

import requests
import json
import time
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime
import urllib.parse
import sys
import os
import re
from functools import wraps
import random

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0):
    """Decorator for retrying failed API calls with exponential backoff"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (backoff_factor ** attempt) + random.uniform(0, 1)
                        time.sleep(wait_time)
                        continue
                    break
            # If we get here, all retries failed
            if hasattr(args[0], 'logger'):
                args[0].logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}: {last_exception}")
            return {"error": f"Max retries exceeded: {str(last_exception)}"}
        return wrapper
    return decorator

def validate_linkedin_id(linkedin_id: str) -> bool:
    """Validate LinkedIn ID format"""
    if not linkedin_id or not isinstance(linkedin_id, str):
        return False

    # LinkedIn provider ID format (starts with ACoA)
    if linkedin_id.startswith('ACoA') and len(linkedin_id) > 20:
        return True

    # Public identifier format (alphanumeric with hyphens, must contain at least one letter)
    if re.match(r'^[a-zA-Z0-9\-]+$', linkedin_id) and len(linkedin_id) >= 3:
        # Must contain at least one letter (not just numbers)
        if re.search(r'[a-zA-Z]', linkedin_id):
            return True

    return False

def sanitize_message(message: str, max_length: int = 1900) -> str:
    """Sanitize and truncate message content"""
    if not message:
        return ""

    # Remove excessive whitespace
    message = re.sub(r'\s+', ' ', message.strip())

    # Truncate if too long
    if len(message) > max_length:
        message = message[:max_length-3] + "..."

    return message

class UnipileClient:
    """Enhanced Unipile API client for LinkedIn integration with retry logic and rate limiting"""

    def __init__(self, api_key: str, rate_limit_per_second: float = 2.0):
        self.api_key = api_key
        self.base_url = "https://api8.unipile.com:13814/api/v1"
        self.headers = {
            "X-API-KEY": self.api_key,
            "accept": "application/json",
            "Content-Type": "application/json"
        }

        # Rate limiting
        self.rate_limit_per_second = rate_limit_per_second
        self.last_request_time = 0
        self.min_request_interval = 1.0 / rate_limit_per_second

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def _rate_limit(self):
        """Apply rate limiting to API requests"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    @retry_on_failure(max_retries=3, delay=1.0, backoff_factor=2.0)
    def make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make HTTP request to Unipile API with rate limiting and enhanced error handling"""
        # Apply rate limiting
        self._rate_limit()

        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            self.logger.info(f"Making {method} request to {url}")
            if data:
                # Log data but hide sensitive information
                safe_data = data.copy()
                if 'password' in safe_data:
                    safe_data['password'] = '***'
                if 'api_key' in safe_data:
                    safe_data['api_key'] = '***'
                self.logger.info(f"Request data: {safe_data}")

            if method.upper() == "GET":
                response = requests.get(url, headers=self.headers, timeout=30)
            elif method.upper() == "POST":
                response = requests.post(url, headers=self.headers, json=data, timeout=30)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=self.headers, json=data, timeout=30)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=self.headers, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            self.logger.info(f"Response status: {response.status_code}")

            # Handle different response types
            try:
                if response.content:
                    response_data = response.json()
                    self.logger.debug(f"Response data: {response_data}")
                else:
                    response_data = {"success": True, "message": "Request completed successfully"}
            except json.JSONDecodeError:
                response_data = {"error": f"Invalid JSON response: {response.text[:200]}"}
                self.logger.error(f"Invalid JSON response: {response.text}")

            # Check for HTTP errors
            if response.status_code >= 400:
                error_msg = response_data.get('error', f"HTTP {response.status_code}: {response.reason}")
                self.logger.error(f"API error: {error_msg}")
                return {"error": error_msg, "status_code": response.status_code}

            return response_data

        except requests.exceptions.Timeout:
            error_msg = "Request timeout - API may be slow or unavailable"
            self.logger.error(error_msg)
            return {"error": error_msg}
        except requests.exceptions.ConnectionError:
            error_msg = "Connection error - check internet connection and API endpoint"
            self.logger.error(error_msg)
            return {"error": error_msg}
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}

    def get_accounts(self) -> Dict:
        """Get all connected accounts"""
        return self.make_request("GET", "accounts")

    def send_linkedin_message(self, chat_id: str, message: str) -> Dict:
        """Send LinkedIn message via Unipile API"""
        data = {
            "text": message
        }
        return self.make_request("POST", f"chats/{chat_id}/messages", data)

    def send_linkedin_inmail(self, account_id: str, recipient_id: str, subject: str, message: str) -> Dict:
        """Send LinkedIn InMail via Unipile API with validation"""
        # Validate inputs
        if not validate_linkedin_id(recipient_id):
            return {"error": f"Invalid LinkedIn ID format: {recipient_id}"}

        if not account_id:
            return {"error": "Account ID is required for InMail"}

        if not subject or len(subject.strip()) == 0:
            return {"error": "Subject is required for InMail"}

        if not message or len(message.strip()) == 0:
            return {"error": "Message content is required for InMail"}

        # Sanitize and validate message length
        sanitized_message = sanitize_message(message, max_length=1900)
        sanitized_subject = sanitize_message(subject, max_length=200)

        # For InMail, we create a formatted message that includes the subject
        formatted_message = f"Subject: {sanitized_subject}\n\n{sanitized_message}"

        data = {
            "account_id": account_id,
            "attendees_ids": [recipient_id],
            "text": formatted_message,
            "options": {
                "linkedin": {
                    "api": "classic",
                    "inmail": True
                }
            }
        }

        result = self.make_request("POST", "chats", data)

        # Add metadata to successful response
        if "error" not in result:
            result["message_type"] = "inmail"
            result["subject"] = sanitized_subject
            result["recipient_id"] = recipient_id

        return result

    def get_linkedin_profile(self, account_id: str, identifier: str) -> Dict:
        """Get LinkedIn profile by public identifier to get provider_id with enhanced search"""
        # Try different endpoint formats for profile lookup
        endpoints_to_try = [
            f"users/{identifier}?account_id={account_id}",
            f"linkedin/users/{identifier}?account_id={account_id}",
            f"accounts/{account_id}/users/{identifier}",
            f"accounts/{account_id}/linkedin/users/{identifier}",
            f"users/search?account_id={account_id}&query={identifier}",
            f"linkedin/profiles/{identifier}?account_id={account_id}"
        ]

        last_error = None
        for endpoint in endpoints_to_try:
            self.logger.debug(f"Trying profile lookup endpoint: {endpoint}")
            result = self.make_request("GET", endpoint)

            if "error" not in result:
                self.logger.info(f"Successfully found profile using endpoint: {endpoint}")
                return result
            else:
                last_error = result.get("error")
                self.logger.debug(f"Endpoint {endpoint} failed: {last_error}")

        # If all endpoints failed, return the last error with helpful suggestions
        return {
            "error": f"Profile lookup failed for {identifier}: {last_error}",
            "suggestions": [
                "1. Verify the LinkedIn profile URL is correct",
                "2. Make sure the profile is public or you're connected",
                "3. Try using the LinkedIn provider ID format (ACoAAABCDEF...)",
                "4. Check if the profile still exists and is active"
            ],
            "identifier_tested": identifier,
            "endpoints_tried": len(endpoints_to_try)
        }

    def get_valid_linkedin_examples(self) -> Dict:
        """Get examples of valid LinkedIn identifiers that are NOT your own account"""
        return {
            "public_identifiers": [
                "john-doe",
                "jane-smith-123",
                "alex-johnson",
                "sarah-wilson-mba"
            ],
            "provider_id_format": "ACoAAABCDEF123456789...",
            "how_to_find": [
                "1. Go to someone else's LinkedIn profile (not your own)",
                "2. Copy the URL: https://linkedin.com/in/john-doe",
                "3. Extract 'john-doe' as the public identifier",
                "4. Make sure it's NOT your own profile"
            ],
            "your_account_id": self._get_linkedin_account_id(),
            "warning": "Never use your own account ID as the recipient"
        }

    def debug_linkedin_identifier(self, identifier: str, account_id: str = None) -> Dict:
        """Debug and validate LinkedIn identifier with detailed analysis"""
        if not account_id:
            account_id = self._get_linkedin_account_id()

        debug_info = {
            "identifier": identifier,
            "account_id": account_id,
            "validation": {
                "is_valid_format": validate_linkedin_id(identifier),
                "identifier_type": "provider_id" if identifier.startswith('ACoA') else "public_identifier",
                "length": len(identifier),
                "contains_letters": bool(re.search(r'[a-zA-Z]', identifier)),
                "contains_numbers": bool(re.search(r'\d', identifier)),
                "contains_hyphens": '-' in identifier
            },
            "suggestions": [],
            "profile_lookup": None,
            "connection_test": None
        }

        # Add specific suggestions based on identifier type
        if identifier.startswith('ACoA'):
            debug_info["suggestions"].extend([
                "Provider ID format detected",
                "This should work directly with Unipile API",
                "If it fails, the user may not exist or be reachable"
            ])
        else:
            debug_info["suggestions"].extend([
                "Public identifier format detected",
                "Will attempt to convert to provider_id",
                "Make sure this matches the LinkedIn profile URL"
            ])

        # Test profile lookup if account_id is available
        if account_id:
            try:
                profile_result = self.get_linkedin_profile(account_id, identifier)
                debug_info["profile_lookup"] = {
                    "success": "error" not in profile_result,
                    "result": profile_result
                }

                if "error" not in profile_result:
                    debug_info["suggestions"].append("Profile lookup successful - identifier is valid")
                else:
                    debug_info["suggestions"].append(f"Profile lookup failed: {profile_result.get('error')}")
            except Exception as e:
                debug_info["profile_lookup"] = {
                    "success": False,
                    "error": str(e)
                }

        # Test if identifier is same as account_id (self-connection attempt)
        if account_id and identifier == account_id:
            debug_info["connection_test"] = {
                "is_self_connection": True,
                "error": "Cannot send connection request to yourself"
            }
            debug_info["suggestions"].append("ERROR: This is your own account ID - use a different person's identifier")

        return debug_info

    def get_linkedin_identifier_help(self, identifier: str) -> Dict:
        """Provide helpful information about LinkedIn identifier formats"""
        return {
            "identifier_provided": identifier,
            "identifier_type": "provider_id" if identifier.startswith('ACoA') else "public_identifier",
            "help": {
                "public_identifier": "Found in LinkedIn profile URL: linkedin.com/in/[public-identifier]",
                "provider_id": "LinkedIn internal ID format starting with 'ACoA' (e.g., ACoAAABCDEF123456789)",
                "how_to_find": [
                    "1. Go to the LinkedIn profile page",
                    "2. Copy the URL (e.g., https://linkedin.com/in/john-doe)",
                    "3. Extract 'john-doe' as the public identifier",
                    "4. Or use browser developer tools to find the provider ID"
                ]
            }
        }

    def send_linkedin_connection_request(self, account_id: str, provider_id: str, message: str) -> Dict:
        """Send LinkedIn connection request with message via Unipile API with enhanced validation

        Args:
            account_id: The connected LinkedIn account ID
            provider_id: LinkedIn provider ID (e.g., ACoAAAcDMMQBODyLwZrRcgYhrkCafURGqva0U4E)
                        or public identifier (e.g., john-doe)
            message: Connection request message
        """
        # Validate inputs
        if not account_id:
            return {"error": "Account ID is required for connection requests"}

        if not validate_linkedin_id(provider_id):
            return {"error": f"Invalid LinkedIn ID format: {provider_id}"}

        if not message or len(message.strip()) == 0:
            return {"error": "Message is required for connection requests"}

        # Check if trying to connect to self
        if provider_id == account_id:
            return {
                "error": "Cannot send connection request to yourself",
                "suggestion": "Please use a different LinkedIn profile ID (not your own account ID)",
                "your_account_id": account_id,
                "attempted_recipient": provider_id
            }

        # Sanitize message
        sanitized_message = sanitize_message(message, max_length=300)  # LinkedIn connection messages are shorter

        # If provider_id looks like a public identifier, try to get the provider_id first
        original_provider_id = provider_id
        if not provider_id.startswith('ACoA'):
            self.logger.info(f"Attempting to convert public identifier {provider_id} to provider_id")
            profile = self.get_linkedin_profile(account_id, provider_id)

            if "error" not in profile:
                actual_provider_id = profile.get("provider_id")
                if actual_provider_id:
                    self.logger.info(f"Successfully converted {provider_id} to provider_id: {actual_provider_id}")
                    provider_id = actual_provider_id
                else:
                    self.logger.warning(f"Profile found but no provider_id field for {provider_id}")
                    # Try other possible fields
                    member_urn = profile.get("member_urn")
                    if member_urn:
                        # Extract provider_id from member_urn if available
                        if ":" in member_urn:
                            potential_id = member_urn.split(":")[-1]
                            if potential_id and len(potential_id) > 10:
                                self.logger.info(f"Using member_urn derived ID: {potential_id}")
                                provider_id = potential_id
            else:
                self.logger.warning(f"Profile lookup failed for {provider_id}: {profile['error']}")
                self.logger.info(f"Will attempt connection request with original identifier: {original_provider_id}")

        # Based on Unipile API documentation, connection requests might need to be sent as messages
        # Let's try the correct Unipile format for LinkedIn connection requests

        # First, try the standard invite endpoint
        data = {
            "account_id": account_id,
            "provider_id": provider_id,
            "message": sanitized_message
        }

        result = self.make_request("POST", "users/invite", data)

        if "error" not in result:
            self.logger.info("Connection request sent successfully via users/invite")
            result["message_type"] = "connection_request"
            result["provider_id"] = provider_id
            result["message"] = sanitized_message
            result["endpoint_used"] = "users/invite"
            return result

        # If that fails, it might be because Unipile requires a different approach
        # for LinkedIn connection requests. Let's provide a helpful error message.
        error_msg = result.get("error", "Unknown error")

        if "404" in str(error_msg) or "Not Found" in str(error_msg):
            # Provide more specific guidance based on identifier type
            identifier_type = "provider_id" if original_provider_id.startswith('ACoA') else "public_identifier"

            if identifier_type == "public_identifier":
                return {
                    "error": f"LinkedIn connection request failed: The user '{original_provider_id}' was not found.",
                    "reason": "The public identifier may be incorrect, the profile may not exist, or the user may have changed their LinkedIn URL",
                    "solutions": [
                        "1. Double-check the LinkedIn profile URL for the correct identifier",
                        "2. Visit the profile directly: https://linkedin.com/in/" + original_provider_id,
                        "3. Make sure the profile is public and active",
                        "4. Try using a different identifier format if the profile URL has changed",
                        "5. Use browser developer tools to find the LinkedIn provider ID (ACoAAABCDEF...)"
                    ],
                    "debug_help": f"Test this identifier: {original_provider_id}",
                    "identifier_type": "public_identifier",
                    "original_error": error_msg
                }
            else:
                return {
                    "error": f"LinkedIn connection request failed: The provider ID '{original_provider_id}' was not found.",
                    "reason": "The provider ID may be invalid, expired, or the user may have deactivated their account",
                    "solutions": [
                        "1. Verify the provider ID is correct and complete",
                        "2. Try getting a fresh provider ID from the LinkedIn profile",
                        "3. Use the public identifier from the profile URL instead",
                        "4. Check if the user's profile is still active"
                    ],
                    "debug_help": f"Test this provider ID: {original_provider_id}",
                    "identifier_type": "provider_id",
                    "original_error": error_msg
                }
        elif "422" in str(error_msg) or "Unprocessable Entity" in str(error_msg):
            # Determine identifier type for specific guidance
            identifier_type = "provider_id" if original_provider_id.startswith('ACoA') else "public_identifier"

            if identifier_type == "provider_id":
                return {
                    "error": f"LinkedIn connection request failed: Provider ID '{original_provider_id}' is invalid or the user cannot receive connection requests",
                    "reason": "The provider ID format is correct but Unipile cannot process the connection request",
                    "solutions": [
                        "1. The user may have privacy settings that block connection requests",
                        "2. The provider ID might be expired or invalid",
                        "3. Try using the public identifier from the LinkedIn profile URL instead",
                        "4. Verify the LinkedIn profile still exists and is active",
                        "5. The user might already be connected to your account"
                    ],
                    "suggestion": "Try using the public identifier (e.g., 'john-doe') from the LinkedIn profile URL instead of the provider ID",
                    "error_type": "validation_error",
                    "identifier_type": "provider_id",
                    "identifier_tested": original_provider_id,
                    "original_error": error_msg
                }
            else:
                return {
                    "error": f"LinkedIn connection request failed: Public identifier '{original_provider_id}' is invalid or not found",
                    "reason": "The public identifier format or content is not valid",
                    "solutions": [
                        "1. Check the LinkedIn profile URL for the correct identifier",
                        "2. Ensure the identifier matches the format 'firstname-lastname' or 'firstname-lastname-numbers'",
                        "3. Verify the LinkedIn profile exists and is public",
                        "4. Try using the provider ID format (ACoAAABCDEF...) instead",
                        "5. Remove any special characters or spaces from the identifier"
                    ],
                    "suggestion": "Get the correct identifier from the LinkedIn profile URL: linkedin.com/in/[identifier]",
                    "error_type": "validation_error",
                    "identifier_type": "public_identifier",
                    "identifier_tested": original_provider_id,
                    "original_error": error_msg
                }
        elif "500" in str(error_msg) or "Internal Server Error" in str(error_msg):
            return {
                "error": f"LinkedIn connection request failed: Server error when trying to send connection request to '{original_provider_id}'. This might be a temporary issue with the Unipile API.",
                "suggestion": "Try again in a few minutes or contact Unipile support",
                "original_error": error_msg
            }
        else:
            return {
                "error": f"LinkedIn connection request failed: {error_msg}",
                "suggestion": "Please check the identifier format and try again",
                "original_error": error_msg
            }

class LinkedInMessaging:
    def __init__(self, config_path: str = "integrations/linkedin_integration/config.json", use_unipile: bool = True):
        """Initialize LinkedIn API client with Unipile and LinkedIn API support"""
        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Fix config path resolution
        if not os.path.isabs(config_path):
            # Try relative to current working directory first
            if os.path.exists(config_path):
                self.config_path = config_path
            else:
                # Try relative to this file's directory
                current_dir = os.path.dirname(os.path.abspath(__file__))
                self.config_path = os.path.join(current_dir, "config.json")
        else:
            self.config_path = config_path

        self.config = self._load_config()
        self.use_unipile = use_unipile

        # Unipile API setup (primary method for messaging)
        self.unipile_client = None
        if use_unipile:
            try:
                unipile_api_key = self.config.get("unipile_api_key", "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk=")
                self.unipile_client = UnipileClient(unipile_api_key)
                self.logger.info("Unipile API client initialized for LinkedIn")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Unipile API: {e}")
                self.use_unipile = False

        # LinkedIn API setup (for content management and connections)
        linkedin_api_config = self.config.get("linkedin_api", {})
        linkedin_api_enabled = linkedin_api_config.get("enabled", True)  # Default to True for backward compatibility

        if linkedin_api_enabled:
            self.client_id = linkedin_api_config.get("client_id") or self.config.get("client_id")
            self.client_secret = linkedin_api_config.get("client_secret") or self.config.get("client_secret")
            self.access_token = linkedin_api_config.get("access_token") or self.config.get("access_token")
            self.refresh_token = linkedin_api_config.get("refresh_token") or self.config.get("refresh_token")
            self.person_id = linkedin_api_config.get("person_id") or self.config.get("person_id")

            # Only set up LinkedIn API if we have valid credentials
            if self.access_token and self.access_token != "YOUR_LINKEDIN_ACCESS_TOKEN" and self.access_token.strip():
                self.api_version = self.config.get("api_version", "v2")
                self.base_url = f"{self.config.get('base_url', 'https://api.linkedin.com')}/{self.api_version}"
                self.logger.info("LinkedIn API fallback enabled with valid credentials")
            else:
                # Clear credentials to disable fallback
                self.client_id = None
                self.client_secret = None
                self.access_token = None
                self.refresh_token = None
                self.person_id = None
                self.logger.info("LinkedIn API fallback disabled - no valid credentials")
        else:
            # LinkedIn API is explicitly disabled
            self.client_id = None
            self.client_secret = None
            self.access_token = None
            self.refresh_token = None
            self.person_id = None
            self.api_version = self.config.get("api_version", "v2")
            self.base_url = f"{self.config.get('base_url', 'https://api.linkedin.com')}/{self.api_version}"
            self.logger.info("LinkedIn API fallback explicitly disabled in config")

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("requests_per_second", 10)

        # Account connection status
        self.connection_status = {
            "unipile": False,
            "linkedin_api": False
        }

        # Cache for LinkedIn account ID
        self._linkedin_account_id = None
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}, error: {e}")
            return {}
        except UnicodeDecodeError as e:
            self.logger.error(f"Unicode decode error in config file: {self.config_path}, error: {e}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()

    def _get_linkedin_account_id(self) -> str:
        """Get the LinkedIn account ID from config or fetch from Unipile"""
        # First check if we have a cached account ID
        if self._linkedin_account_id:
            return self._linkedin_account_id

        # Check if account_id is set in config
        unipile_config = self.config.get("unipile", {})
        config_account_id = unipile_config.get("account_id")
        if config_account_id and config_account_id.strip():
            self._linkedin_account_id = config_account_id.strip()
            self.logger.info(f"Using LinkedIn account ID from config: {self._linkedin_account_id}")
            return self._linkedin_account_id

        # If not in config, fetch from Unipile API
        if not self.unipile_client:
            return None

        try:
            accounts = self.unipile_client.get_accounts()
            if "error" not in accounts:
                # Accounts are in 'items' field, not 'accounts'
                all_accounts = accounts.get("items", [])
                if all_accounts:
                    # Filter for LinkedIn accounts using 'type' field
                    linkedin_accounts = [acc for acc in all_accounts if acc.get("type") == "LINKEDIN"]
                    if linkedin_accounts:
                        self._linkedin_account_id = linkedin_accounts[0].get("id")
                        self.logger.info(f"Auto-detected LinkedIn account ID: {self._linkedin_account_id}")

                        # Optionally save to config for future use
                        if not config_account_id:
                            self.config.setdefault("unipile", {})["account_id"] = self._linkedin_account_id
                            self._save_config()
                            self.logger.info("Saved account ID to config for future use")

                        return self._linkedin_account_id
                    else:
                        self.logger.warning("No LinkedIn accounts found in connected accounts")
        except Exception as e:
            self.logger.error(f"Error getting LinkedIn account ID: {e}")

        return None

    def _make_request(self, method: str, endpoint: str, data: Dict = None,
                     params: Dict = None, headers: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        if not self.access_token:
            return {"error": "Access token not configured"}
        
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        # Default headers
        default_headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
            "X-Restli-Protocol-Version": "2.0.0"
        }
        
        if headers:
            default_headers.update(headers)
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=default_headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=default_headers, json=data, params=params)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=default_headers, json=data, params=params)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=default_headers, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json() if response.content else {"success": True}
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}

    def authenticate_account(self, account_id: str = None) -> Dict:
        """Authenticate LinkedIn account via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Check if account is already connected
            accounts = self.unipile_client.get_accounts()

            if "error" in accounts:
                return {"error": accounts["error"]}

            # Handle both 'accounts' and 'items' field formats from Unipile API
            all_accounts = accounts.get("items", accounts.get("accounts", []))
            linkedin_accounts = [acc for acc in all_accounts
                               if acc.get("provider") == "linkedin" or acc.get("type") == "LINKEDIN"]

            if linkedin_accounts:
                self.connection_status["unipile"] = True
                self.logger.info("LinkedIn account already connected via Unipile")
                return {
                    "success": True,
                    "message": "LinkedIn account connected",
                    "accounts": linkedin_accounts
                }
            else:
                # Return authentication URL or instructions
                return {
                    "success": False,
                    "message": "No LinkedIn account connected. Please connect via Unipile dashboard.",
                    "auth_required": True
                }
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}

    def send_inmail(self, recipient_id: str, subject: str, message_body: str, account_id: str = None, **kwargs) -> Dict:
        """Send InMail via Unipile (primary) or LinkedIn API (fallback) with comprehensive validation"""
        # Input validation
        if not recipient_id or not isinstance(recipient_id, str):
            return {"error": "Valid recipient_id is required"}

        if not subject or not isinstance(subject, str) or len(subject.strip()) == 0:
            return {"error": "Valid subject is required for InMail"}

        if not message_body or not isinstance(message_body, str) or len(message_body.strip()) == 0:
            return {"error": "Valid message body is required"}

        # Check message length limits from config
        inmail_settings = self.config.get("inmail_settings", {})
        max_subject_length = inmail_settings.get("max_subject_length", 200)
        max_message_length = inmail_settings.get("max_message_length", 1900)

        if len(subject) > max_subject_length:
            return {"error": f"Subject too long. Maximum {max_subject_length} characters allowed"}

        if len(message_body) > max_message_length:
            return {"error": f"Message too long. Maximum {max_message_length} characters allowed"}

        # Try Unipile first
        if self.use_unipile and self.unipile_client:
            try:
                # Get account_id from connected LinkedIn accounts if not provided
                if not account_id:
                    account_id = self._get_linkedin_account_id()

                if account_id:
                    result = self.unipile_client.send_linkedin_inmail(
                        account_id=account_id,
                        recipient_id=recipient_id,
                        subject=subject,
                        message=message_body
                    )

                    if "error" not in result:
                        self.logger.info(f"InMail sent via Unipile to {recipient_id}")
                        return {
                            "success": True,
                            "result": result,
                            "method": "unipile",
                            "recipient_id": recipient_id,
                            "subject": subject,
                            "timestamp": datetime.now().isoformat()
                        }
                    else:
                        self.logger.warning(f"Unipile failed: {result.get('error')}, trying LinkedIn API")
                else:
                    self.logger.warning("No LinkedIn account_id available for Unipile, trying LinkedIn API")
            except Exception as e:
                self.logger.warning(f"Unipile error: {e}, trying LinkedIn API")

        # Fallback to LinkedIn API
        fallback_result = self.send_message(recipient_id, subject, message_body)
        if "error" not in fallback_result:
            fallback_result["method"] = "linkedin_api"
            fallback_result["timestamp"] = datetime.now().isoformat()

        return fallback_result

    def send_connection_message(self, recipient_id: str, message: str, account_id: str = None, **kwargs) -> Dict:
        """Send LinkedIn connection request with message via Unipile (primary) or LinkedIn API (fallback) with validation"""
        # Input validation
        if not recipient_id or not isinstance(recipient_id, str):
            return {"error": "Valid recipient_id is required"}

        if not message or not isinstance(message, str) or len(message.strip()) == 0:
            return {"error": "Valid message is required for connection requests"}

        # Check message length (LinkedIn connection messages have stricter limits)
        max_connection_message_length = 300
        if len(message) > max_connection_message_length:
            return {"error": f"Connection message too long. Maximum {max_connection_message_length} characters allowed"}

        # Get account_id if not provided and check for self-connection
        if not account_id:
            account_id = self._get_linkedin_account_id()

        if account_id and recipient_id == account_id:
            return {
                "error": "Cannot send connection request to yourself",
                "suggestion": "Please use a different LinkedIn profile ID (not your own account ID)",
                "your_account_id": account_id,
                "attempted_recipient": recipient_id
            }

        # Try Unipile first
        if self.use_unipile and self.unipile_client:
            try:
                # Get account_id from connected LinkedIn accounts if not provided
                if not account_id:
                    account_id = self._get_linkedin_account_id()

                if account_id:
                    result = self.unipile_client.send_linkedin_connection_request(
                        account_id=account_id,
                        provider_id=recipient_id,  # Use provider_id instead of recipient_id
                        message=message
                    )

                    if "error" not in result:
                        self.logger.info(f"Connection request sent via Unipile to {recipient_id}")
                        return {
                            "success": True,
                            "result": result,
                            "method": "unipile",
                            "recipient_id": recipient_id,
                            "message": message,
                            "timestamp": datetime.now().isoformat()
                        }
                    else:
                        self.logger.warning(f"Unipile failed: {result.get('error')}, trying LinkedIn API")
                else:
                    self.logger.warning("No LinkedIn account_id available for Unipile, trying LinkedIn API")
            except Exception as e:
                self.logger.warning(f"Unipile error: {e}, trying LinkedIn API")

        # Fallback to LinkedIn API (if enabled and configured)
        if not self.access_token or self.access_token == "YOUR_LINKEDIN_ACCESS_TOKEN":
            # LinkedIn API fallback is not available
            return {
                "error": f"LinkedIn connection request failed: The user '{recipient_id}' may not exist or may not be reachable for connection requests. LinkedIn API fallback is not configured.",
                "reason": "Unipile API returned an error and LinkedIn API fallback is disabled",
                "solutions": [
                    "1. Verify the LinkedIn profile URL is correct: https://linkedin.com/in/" + recipient_id if not recipient_id.startswith('ACoA') else "1. Verify the LinkedIn provider ID is correct",
                    "2. Make sure the profile exists and is public",
                    "3. Try using a different identifier format (public identifier vs provider ID)",
                    "4. Wait a few minutes and try again (temporary API issues)",
                    "5. Configure LinkedIn API credentials in config.json for automatic fallback",
                    "6. Use the debug tool: python debug_linkedin.py " + recipient_id
                ],
                "suggestion": "Double-check the LinkedIn identifier and try again, or configure LinkedIn API fallback.",
                "error_type": "identifier_not_found",
                "retry_recommended": True,
                "retry_delay_seconds": 60,
                "fallback_available": False,
                "attempted_recipient": recipient_id,
                "identifier_type": "provider_id" if recipient_id.startswith('ACoA') else "public_identifier",
                "debug_command": f"python integrations/linkedin_integration/debug_linkedin.py {recipient_id}",
                "timestamp": datetime.now().isoformat()
            }

        # LinkedIn API fallback is available
        subject = "Professional Connection"
        fallback_result = self.send_message(recipient_id, subject, message)
        if "error" not in fallback_result:
            fallback_result["method"] = "linkedin_api"
            fallback_result["timestamp"] = datetime.now().isoformat()

        return fallback_result

    def send_connection_message_with_retry(self, recipient_id: str, message: str, account_id: str = None, max_retries: int = 3, **kwargs) -> Dict:
        """Send LinkedIn connection request with automatic retry logic for server errors"""

        for attempt in range(max_retries):
            self.logger.info(f"Connection request attempt {attempt + 1}/{max_retries} for {recipient_id}")

            result = self.send_connection_message(recipient_id, message, account_id, **kwargs)

            if result.get("success"):
                if attempt > 0:
                    self.logger.info(f"Connection request succeeded on attempt {attempt + 1}")
                return result

            # Check if it's a retryable error
            error_type = result.get("error_type", "unknown")
            retry_recommended = result.get("retry_recommended", False)

            if error_type == "server_error" and retry_recommended and attempt < max_retries - 1:
                delay = result.get("retry_delay_seconds", 60)
                self.logger.info(f"Server error detected, retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                # Not retryable or max retries reached
                if attempt == max_retries - 1:
                    result["max_retries_reached"] = True
                    result["total_attempts"] = max_retries
                return result

        return result

    def send_connection_message_with_alternatives(self, recipient_id: str, message: str, account_id: str = None, **kwargs) -> Dict:
        """Send LinkedIn connection request with alternative identifier formats and methods"""

        # Method 1: Try original identifier
        self.logger.info(f"Trying original identifier: {recipient_id}")
        result = self.send_connection_message(recipient_id, message, account_id, **kwargs)

        if result.get("success"):
            return result

        # Method 2: Try identifier variations if validation error
        if result.get("error_type") == "validation_error":
            variations = self._generate_identifier_variations(recipient_id)

            for i, variation in enumerate(variations):
                if variation == recipient_id:
                    continue  # Skip original

                self.logger.info(f"Trying identifier variation {i+1}: {variation}")
                var_result = self.send_connection_message(variation, message, account_id, **kwargs)

                if var_result.get("success"):
                    var_result["identifier_used"] = variation
                    var_result["original_identifier"] = recipient_id
                    var_result["method"] = f"{var_result.get('method', 'unknown')}_variation"
                    return var_result

        # Method 3: Try with retry logic if server error
        if result.get("error_type") == "server_error":
            self.logger.info("Server error detected, trying with retry logic...")
            retry_result = self.send_connection_message_with_retry(recipient_id, message, account_id, max_retries=2, **kwargs)

            if retry_result.get("success"):
                return retry_result

        # Method 4: Try alternative messaging approach
        if not result.get("success"):
            self.logger.info("Trying alternative messaging approach...")
            alt_result = self._try_alternative_messaging_approach(recipient_id, message, account_id)

            if alt_result.get("success"):
                return alt_result

        # All methods failed
        result["alternative_methods_tried"] = True
        result["methods_attempted"] = ["original", "variations", "retry", "alternative_messaging"]
        return result

    def _generate_identifier_variations(self, recipient_id: str) -> List[str]:
        """Generate different variations of the identifier to try"""
        variations = []

        if recipient_id.startswith('ACoA'):
            # Provider ID - limited variations
            variations.append(recipient_id)
        else:
            # Public identifier - try different formats
            base_id = recipient_id.lower().strip()

            # Remove numbers at the end
            import re
            base_without_numbers = re.sub(r'-\d+$', '', base_id)
            if base_without_numbers != base_id:
                variations.append(base_without_numbers)

            # Try with common number patterns
            if not re.search(r'-\d+$', base_id):
                variations.extend([
                    f"{base_id}-1",
                    f"{base_id}-123",
                    f"{base_id}-01"
                ])

            # Try without hyphens
            no_hyphens = base_id.replace('-', '')
            if no_hyphens != base_id and len(no_hyphens) > 2:
                variations.append(no_hyphens)

            # Try with underscores
            with_underscores = base_id.replace('-', '_')
            if with_underscores != base_id:
                variations.append(with_underscores)

            # Original identifier
            variations.append(recipient_id)

        # Remove duplicates while preserving order
        seen = set()
        unique_variations = []
        for var in variations:
            if var not in seen:
                seen.add(var)
                unique_variations.append(var)

        return unique_variations[:5]  # Limit to 5 variations

    def _try_alternative_messaging_approach(self, recipient_id: str, message: str, account_id: str = None) -> Dict:
        """Try alternative messaging approaches when connection request fails"""

        # Alternative 1: Try as InMail if connection request fails
        try:
            self.logger.info("Trying InMail as alternative to connection request...")
            inmail_result = self.send_inmail(
                recipient_id=recipient_id,
                subject="Professional Connection",
                message_body=f"Connection Request: {message}",
                account_id=account_id
            )

            if inmail_result.get("success"):
                inmail_result["method"] = "inmail_alternative"
                inmail_result["note"] = "Sent as InMail due to connection request issues"
                return inmail_result
        except Exception as e:
            self.logger.warning(f"InMail alternative failed: {e}")

        # Alternative 2: Try direct message if already connected
        try:
            self.logger.info("Trying direct message as alternative...")
            dm_result = self.send_message(
                recipient_id=recipient_id,
                subject="Professional Connection",
                message_body=message
            )

            if dm_result.get("success"):
                dm_result["method"] = "direct_message_alternative"
                dm_result["note"] = "Sent as direct message (may already be connected)"
                return dm_result
        except Exception as e:
            self.logger.warning(f"Direct message alternative failed: {e}")

        return {"error": "All alternative messaging approaches failed", "error_type": "all_methods_failed"}

    def send_company_page_message(self, company_id: str, recipient_id: str, message: str, **kwargs) -> Dict:
        """Send message from company page via Unipile"""
        if not self.unipile_client:
            return {"error": "Unipile client not available for company page messaging"}

        try:
            # Company page messaging is a premium feature that requires special setup
            # For now, we'll return an informative message about this limitation
            return {
                "error": "Company page messaging requires LinkedIn Company Page access and special Unipile configuration. Please contact Unipile support to enable this feature for your account.",
                "feature": "company_page_messaging",
                "company_id": company_id,
                "recipient_id": recipient_id
            }
        except Exception as e:
            self.logger.error(f"Company page message error: {e}")
            return {"error": str(e)}

    def send_bulk_inmails(self, recipients: List[Dict], subject: str, message_template: str, delay: float = 5.0, **kwargs) -> Dict:
        """Send InMails to multiple recipients with comprehensive error handling and progress tracking"""
        if not recipients or not isinstance(recipients, list):
            return {"error": "Valid recipients list is required"}

        if not subject or not isinstance(subject, str):
            return {"error": "Valid subject template is required"}

        if not message_template or not isinstance(message_template, str):
            return {"error": "Valid message template is required"}

        # Get messaging limits from config
        messaging_limits = self.config.get("messaging_limits", {})
        max_inmails_per_day = messaging_limits.get("inmails_per_day", 20)

        if len(recipients) > max_inmails_per_day:
            return {"error": f"Too many recipients. Maximum {max_inmails_per_day} InMails per day allowed"}

        results = []
        successful_sends = 0
        failed_sends = 0

        self.logger.info(f"Starting bulk InMail campaign to {len(recipients)} recipients")

        for i, recipient in enumerate(recipients):
            recipient_id = recipient.get("id")
            if not recipient_id:
                results.append({
                    "recipient_id": None,
                    "result": {"error": "Missing recipient ID"},
                    "status": "failed",
                    "type": "inmail",
                    "timestamp": datetime.now().isoformat()
                })
                failed_sends += 1
                continue

            try:
                # Personalize message and subject
                personalized_message = message_template.format(**recipient)
                personalized_subject = subject.format(**recipient)

                result = self.send_inmail(recipient_id, personalized_subject, personalized_message)

                status = "success" if result.get("success", False) else "failed"
                if status == "success":
                    successful_sends += 1
                else:
                    failed_sends += 1

                results.append({
                    "recipient_id": recipient_id,
                    "result": result,
                    "status": status,
                    "method": result.get("method", "unknown"),
                    "type": "inmail",
                    "timestamp": datetime.now().isoformat()
                })

                self.logger.info(f"InMail {i+1}/{len(recipients)} - {status} - {recipient_id}")

            except Exception as e:
                self.logger.error(f"Error sending InMail to {recipient_id}: {e}")
                results.append({
                    "recipient_id": recipient_id,
                    "result": {"error": str(e)},
                    "status": "failed",
                    "type": "inmail",
                    "timestamp": datetime.now().isoformat()
                })
                failed_sends += 1

            # Apply delay between messages
            if delay > 0 and i < len(recipients) - 1:
                self.logger.debug(f"Waiting {delay} seconds before next message")
                time.sleep(delay)

        summary = {
            "total_recipients": len(recipients),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "success_rate": (successful_sends / len(recipients)) * 100 if recipients else 0,
            "results": results,
            "timestamp": datetime.now().isoformat()
        }

        self.logger.info(f"Bulk InMail campaign completed: {successful_sends}/{len(recipients)} successful")
        return summary

    def debug_connection_issue(self, recipient_id: str, account_id: str = None) -> Dict:
        """Debug connection request issues with comprehensive analysis"""
        if not account_id:
            account_id = self._get_linkedin_account_id()

        debug_result = {
            "recipient_id": recipient_id,
            "account_id": account_id,
            "timestamp": datetime.now().isoformat(),
            "checks": {}
        }

        # Check 1: Validate identifier format
        debug_result["checks"]["identifier_validation"] = {
            "is_valid": validate_linkedin_id(recipient_id),
            "type": "provider_id" if recipient_id.startswith('ACoA') else "public_identifier",
            "length": len(recipient_id)
        }

        # Check 2: Self-connection test
        debug_result["checks"]["self_connection_test"] = {
            "is_self": recipient_id == account_id,
            "warning": "Cannot connect to yourself" if recipient_id == account_id else "OK"
        }

        # Check 3: Unipile client availability
        debug_result["checks"]["unipile_client"] = {
            "available": bool(self.unipile_client),
            "account_id_configured": bool(account_id)
        }

        # Check 4: Profile lookup test (if possible)
        if self.unipile_client and account_id:
            try:
                profile_result = self.unipile_client.debug_linkedin_identifier(recipient_id, account_id)
                debug_result["checks"]["profile_lookup"] = profile_result
            except Exception as e:
                debug_result["checks"]["profile_lookup"] = {"error": str(e)}

        # Check 5: LinkedIn API fallback availability
        debug_result["checks"]["linkedin_api_fallback"] = {
            "available": bool(self.access_token and self.client_id),
            "configured": bool(self.access_token and self.access_token != "YOUR_LINKEDIN_ACCESS_TOKEN")
        }

        # Generate recommendations
        recommendations = []

        if not debug_result["checks"]["identifier_validation"]["is_valid"]:
            recommendations.append("Fix identifier format - use LinkedIn profile URL format or provider ID")

        if debug_result["checks"]["self_connection_test"]["is_self"]:
            recommendations.append("Use a different person's LinkedIn identifier (not your own)")

        if not debug_result["checks"]["unipile_client"]["available"]:
            recommendations.append("Configure Unipile API client")

        if not debug_result["checks"]["linkedin_api_fallback"]["configured"]:
            recommendations.append("Configure LinkedIn API credentials for fallback")

        profile_lookup = debug_result["checks"].get("profile_lookup", {})
        if isinstance(profile_lookup, dict) and "error" in profile_lookup:
            recommendations.append("Profile lookup failed - verify the LinkedIn identifier is correct")

        debug_result["recommendations"] = recommendations
        debug_result["next_steps"] = [
            "1. Fix any issues identified in the checks above",
            "2. Test the identifier by visiting: https://linkedin.com/in/" + recipient_id if not recipient_id.startswith('ACoA') else "2. Verify the provider ID is correct",
            "3. Try the connection request again",
            "4. Check Unipile API status if issues persist"
        ]

        return debug_result

    def get_connection_status(self) -> Dict:
        """Get current connection status for both Unipile and LinkedIn API"""
        status = {
            "unipile": {
                "available": bool(self.unipile_client),
                "connected": False,
                "accounts": []
            },
            "linkedin_api": {
                "available": bool(self.access_token and self.client_id),
                "connected": False,
                "person_id": self.person_id
            }
        }

        # Check Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                if "error" not in accounts:
                    all_accounts = accounts.get("items", [])
                    linkedin_accounts = [acc for acc in all_accounts if acc.get("type") == "LINKEDIN"]
                    status["unipile"]["connected"] = len(linkedin_accounts) > 0
                    status["unipile"]["accounts"] = linkedin_accounts
                else:
                    self.logger.error(f"Unipile API error: {accounts['error']}")
            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")

        # Check LinkedIn API connection
        if self.access_token and self.client_id:
            try:
                profile = self.get_profile()
                status["linkedin_api"]["connected"] = "error" not in profile
            except Exception as e:
                self.logger.error(f"Error checking LinkedIn API status: {e}")

        return status

    def get_valid_linkedin_examples(self) -> Dict:
        """Get examples of valid LinkedIn identifiers that are NOT your own account"""
        return {
            "public_identifiers": [
                "john-doe",
                "jane-smith-123",
                "alex-johnson",
                "sarah-wilson-mba"
            ],
            "provider_id_format": "ACoAAABCDEF123456789...",
            "how_to_find": [
                "1. Go to someone else's LinkedIn profile (not your own)",
                "2. Copy the URL: https://linkedin.com/in/john-doe",
                "3. Extract 'john-doe' as the public identifier",
                "4. Make sure it's NOT your own profile"
            ],
            "your_account_id": self._get_linkedin_account_id(),
            "warning": "Never use your own account ID as the recipient"
        }

    def update_config_settings(self, **kwargs) -> Dict:
        """Update LinkedIn configuration settings"""
        try:
            # Update configuration values
            for key, value in kwargs.items():
                if value is not None:
                    self.config[key] = value
                    # Update instance variables
                    if hasattr(self, key):
                        setattr(self, key, value)

            # Save configuration
            self._save_config()

            return {
                "success": True,
                "message": "Configuration updated successfully",
                "updated_fields": list(kwargs.keys())
            }

        except Exception as e:
            self.logger.error(f"Config update error: {e}")
            return {"error": str(e)}

    def get_profile(self, fields: List[str] = None) -> Dict:
        """Get user profile information"""
        if not fields:
            fields = ["id", "firstName", "lastName", "headline", "profilePicture", "industry", "summary"]
        
        params = {
            "projection": f"({','.join(fields)})"
        }
        
        return self._make_request("GET", "people/~", params=params)
    
    def send_message(self, recipient_id: str, subject: str, message_body: str) -> Dict:
        """Send direct message to LinkedIn connection"""
        if not self.person_id:
            return {"error": "Person ID not configured"}
        
        data = {
            "recipients": [f"urn:li:person:{recipient_id}"],
            "subject": subject,
            "body": message_body,
            "sender": f"urn:li:person:{self.person_id}"
        }
        
        result = self._make_request("POST", "messaging/conversations", data)
        
        if "error" not in result:
            self.logger.info(f"Message sent successfully to {recipient_id}")
        else:
            self.logger.error(f"Failed to send message: {result}")
        
        return result
    
    def get_connections(self, start: int = 0, count: int = 50) -> Dict:
        """Get user's connections"""
        params = {
            "start": start,
            "count": count,
            "projection": "(elements*(to~(id,firstName,lastName,headline,profilePicture)))"
        }
        
        return self._make_request("GET", "people/~/connections", params=params)

    def send_connection_request_enhanced(self, person_id: str, message: str = None,
                                       template_type: str = "general", **kwargs) -> Dict:
        """Enhanced connection request with personalized messaging"""
        if not message:
            templates = self.config.get("connection_request_templates", {})
            template = templates.get(template_type, templates.get("general", "I'd like to connect with you."))
            message = template.format(**kwargs)

        # Try Unipile first for connection request
        if self.use_unipile and self.unipile_client:
            try:
                result = self.unipile_client.send_linkedin_connection_request(
                    person_id,
                    message,
                    **kwargs
                )
                if "error" not in result:
                    self.logger.info(f"Enhanced connection request sent via Unipile to {person_id}")
                    return {"success": True, "result": result, "method": "unipile"}
                else:
                    self.logger.warning(f"Unipile connection request failed: {result.get('error')}")
            except Exception as e:
                self.logger.warning(f"Unipile connection request error: {e}")

        # Fallback to LinkedIn API
        return self.send_connection_request(person_id, message)

    def send_connection_request(self, person_id: str, message: str = None) -> Dict:
        """Send connection request to a person"""
        if not message:
            message = self.config.get("connection_request_templates", {}).get("general", "I'd like to connect with you.")
        
        data = {
            "invitee": {
                "com.linkedin.voyager.growth.invitation.InviteeProfile": {
                    "profileId": person_id
                }
            },
            "message": message
        }
        
        result = self._make_request("POST", "people/~/mailbox", data)
        
        if "error" not in result:
            self.logger.info(f"Connection request sent to {person_id}")
        else:
            self.logger.error(f"Failed to send connection request: {result}")
        
        return result
    
    def create_post(self, content: str, visibility: str = None, 
                   media_url: str = None, link_url: str = None) -> Dict:
        """Create a LinkedIn post"""
        if not visibility:
            visibility = self.config.get("post_settings", {}).get("default_visibility", "PUBLIC")
        
        max_length = self.config.get("post_settings", {}).get("max_content_length", 3000)
        if len(content) > max_length:
            content = content[:max_length-3] + "..."
        
        post_data = {
            "author": f"urn:li:person:{self.person_id}",
            "lifecycleState": "PUBLISHED",
            "specificContent": {
                "com.linkedin.ugc.ShareContent": {
                    "shareCommentary": {
                        "text": content
                    },
                    "shareMediaCategory": "NONE"
                }
            },
            "visibility": {
                "com.linkedin.ugc.MemberNetworkVisibility": visibility
            }
        }
        
        # Add media if provided
        if media_url:
            post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["shareMediaCategory"] = "IMAGE"
            post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["media"] = [{
                "status": "READY",
                "media": media_url
            }]
        
        # Add link if provided
        if link_url:
            post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["shareMediaCategory"] = "ARTICLE"
            post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["media"] = [{
                "status": "READY",
                "originalUrl": link_url
            }]
        
        result = self._make_request("POST", "ugcPosts", post_data)
        
        if "error" not in result:
            self.logger.info("LinkedIn post created successfully")
        else:
            self.logger.error(f"Failed to create post: {result}")
        
        return result
    
    def get_company_info(self, company_id: str, fields: List[str] = None) -> Dict:
        """Get company information"""
        if not fields:
            fields = ["id", "name", "description", "industry", "website", "employeeCountRange", "foundedOn"]
        
        params = {
            "projection": f"({','.join(fields)})"
        }
        
        return self._make_request("GET", f"companies/{company_id}", params=params)
    
    def search_people(self, keywords: str = None, company: str = None, 
                     industry: str = None, location: str = None, count: int = 25) -> Dict:
        """Search for people on LinkedIn"""
        params = {
            "count": count,
            "start": 0
        }
        
        # Build search query
        query_parts = []
        if keywords:
            query_parts.append(f"keywords:{keywords}")
        if company:
            query_parts.append(f"company:{company}")
        if industry:
            query_parts.append(f"industry:{industry}")
        if location:
            query_parts.append(f"location:{location}")
        
        if query_parts:
            params["q"] = "people"
            params["keywords"] = " ".join(query_parts)
        
        return self._make_request("GET", "peopleSearch", params=params)
    
    def get_post_analytics(self, post_id: str) -> Dict:
        """Get analytics for a specific post"""
        params = {
            "q": "ugcPost",
            "ugcPost": f"urn:li:ugcPost:{post_id}"
        }
        
        return self._make_request("GET", "socialActions", params=params)
    
    def like_post(self, post_id: str) -> Dict:
        """Like a LinkedIn post"""
        data = {
            "actor": f"urn:li:person:{self.person_id}",
            "object": f"urn:li:ugcPost:{post_id}"
        }
        
        result = self._make_request("POST", "socialActions", data)
        
        if "error" not in result:
            self.logger.info(f"Post {post_id} liked successfully")
        else:
            self.logger.error(f"Failed to like post: {result}")
        
        return result
    
    def comment_on_post(self, post_id: str, comment_text: str) -> Dict:
        """Comment on a LinkedIn post"""
        data = {
            "actor": f"urn:li:person:{self.person_id}",
            "object": f"urn:li:ugcPost:{post_id}",
            "message": {
                "text": comment_text
            }
        }
        
        result = self._make_request("POST", "socialActions", data)
        
        if "error" not in result:
            self.logger.info(f"Comment posted on {post_id}")
        else:
            self.logger.error(f"Failed to comment on post: {result}")
        
        return result
    
    def send_bulk_messages(self, recipients: List[Dict], subject: str, 
                          message_template: str, delay: float = 3.0) -> List[Dict]:
        """Send messages to multiple recipients with personalization"""
        results = []
        
        for recipient in recipients:
            recipient_id = recipient.get("id")
            if not recipient_id:
                continue
            
            # Personalize message
            personalized_message = message_template.format(**recipient)
            
            result = self.send_message(recipient_id, subject, personalized_message)
            results.append({
                "recipient_id": recipient_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })
            
            if delay > 0:
                time.sleep(delay)
        
        return results
    
    def send_template_message(self, recipient_id: str, template_name: str, 
                            subject: str = None, **kwargs) -> Dict:
        """Send predefined template message"""
        templates = self.config.get("message_templates", {})
        
        if template_name not in templates:
            return {"error": f"Template '{template_name}' not found"}
        
        message = templates[template_name].format(**kwargs)
        
        if not subject:
            subject = f"Message from {self.get_profile().get('firstName', 'LinkedIn User')}"
        
        return self.send_message(recipient_id, subject, message)
    
    def refresh_access_token(self) -> Dict:
        """Refresh access token using refresh token"""
        if not self.refresh_token or not self.client_id or not self.client_secret:
            return {"error": "Refresh token or client credentials not configured"}
        
        data = {
            "grant_type": "refresh_token",
            "refresh_token": self.refresh_token,
            "client_id": self.client_id,
            "client_secret": self.client_secret
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            response = requests.post("https://www.linkedin.com/oauth/v2/accessToken", 
                                   data=data, headers=headers)
            response.raise_for_status()
            result = response.json()
            
            if "access_token" in result:
                self.access_token = result["access_token"]
                self.config["access_token"] = self.access_token
                
                if "refresh_token" in result:
                    self.refresh_token = result["refresh_token"]
                    self.config["refresh_token"] = self.refresh_token
                
                self._save_config()
                self.logger.info("Access token refreshed successfully")
            
            return result
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Token refresh failed: {e}")
            return {"error": str(e)}
    
    def is_configured(self) -> bool:
        """Check if LinkedIn API is properly configured"""
        return bool(self.access_token and self.client_id)
    
    def update_config(self, client_id: str = None, client_secret: str = None,
                     access_token: str = None, refresh_token: str = None, person_id: str = None):
        """Update configuration"""
        if client_id:
            self.config["client_id"] = client_id
            self.client_id = client_id
        
        if client_secret:
            self.config["client_secret"] = client_secret
            self.client_secret = client_secret
        
        if access_token:
            self.config["access_token"] = access_token
            self.access_token = access_token
        
        if refresh_token:
            self.config["refresh_token"] = refresh_token
            self.refresh_token = refresh_token
        
        if person_id:
            self.config["person_id"] = person_id
            self.person_id = person_id
        
        self._save_config()
        self.logger.info("LinkedIn configuration updated")

# Alias for backward compatibility
LinkedInAPI = LinkedInMessaging

def test_linkedin_messaging():
    """Comprehensive test function for LinkedIn messaging functionality"""
    print("🔍 Testing LinkedIn Messaging Integration...")

    # Initialize LinkedIn messaging
    linkedin = LinkedInMessaging(use_unipile=True)

    # Test 1: Configuration validation
    print("\n1. Testing configuration...")
    if linkedin.is_configured():
        print("✅ LinkedIn API configured")
    else:
        print("⚠️  LinkedIn API not fully configured")

    # Test 2: Connection status
    print("\n2. Testing connection status...")
    status = linkedin.get_connection_status()
    print(f"Connection status: {status}")

    # Test 3: Authentication
    print("\n3. Testing authentication...")
    auth_result = linkedin.authenticate_account()
    print(f"Authentication result: {auth_result}")

    # Test 4: Input validation
    print("\n4. Testing input validation...")

    # Test invalid InMail inputs
    invalid_inmail = linkedin.send_inmail("", "", "")
    print(f"Invalid InMail test: {invalid_inmail.get('error', 'No error')}")

    # Test invalid connection message inputs
    invalid_connection = linkedin.send_connection_message("", "")
    print(f"Invalid connection test: {invalid_connection.get('error', 'No error')}")

    # Test 5: Message length validation
    print("\n5. Testing message length validation...")
    long_subject = "A" * 300  # Too long for subject
    long_message = "B" * 2000  # Too long for message

    long_inmail = linkedin.send_inmail("test-id", long_subject, long_message)
    print(f"Long message test: {long_inmail.get('error', 'No error')}")

    # Test 6: LinkedIn ID validation
    print("\n6. Testing LinkedIn ID validation...")
    from linkedin_integration.linkedin_api import validate_linkedin_id

    valid_ids = ["ACoAAABCDEF123456789", "john-doe", "jane-smith-123"]
    invalid_ids = ["", "a", "<EMAIL>", "123"]

    for id_test in valid_ids:
        result = validate_linkedin_id(id_test)
        print(f"ID '{id_test}': {'✅ Valid' if result else '❌ Invalid'}")

    for id_test in invalid_ids:
        result = validate_linkedin_id(id_test)
        print(f"ID '{id_test}': {'✅ Valid' if result else '❌ Invalid'}")

    # Test 7: Message sanitization
    print("\n7. Testing message sanitization...")
    from linkedin_integration.linkedin_api import sanitize_message

    test_messages = [
        "Normal message",
        "Message   with    extra    spaces",
        "A" * 2000,  # Too long
        "   Leading and trailing spaces   "
    ]

    for msg in test_messages:
        sanitized = sanitize_message(msg)
        print(f"Original: '{msg[:50]}...' -> Sanitized: '{sanitized[:50]}...'")

    print("\n✅ LinkedIn messaging tests completed!")
    return linkedin

# Example usage and testing
if __name__ == "__main__":
    # Run comprehensive tests
    linkedin = test_linkedin_messaging()

    print("\n" + "="*50)
    print("MANUAL TESTING EXAMPLES")
    print("="*50)

    print("\n📝 To test actual messaging (replace with real IDs):")
    print("# Send InMail:")
    print('# result = linkedin.send_inmail("PERSON_ID", "Professional Opportunity", "Hello! I\'d like to discuss a professional opportunity.")')
    print('# print(f"InMail result: {result}")')

    print("\n# Send connection message:")
    print('# conn_result = linkedin.send_connection_message("PERSON_ID", "Great to connect with you!")')
    print('# print(f"Connection message result: {conn_result}")')

    print("\n# Bulk InMail example:")
    print("# recipients = [")
    print('#     {"id": "person1", "name": "John", "company": "ABC Corp"},')
    print('#     {"id": "person2", "name": "Jane", "company": "XYZ Inc"}')
    print("# ]")
    print('# bulk_result = linkedin.send_bulk_inmails(recipients, "Hello {name}", "Hi {name}, I saw you work at {company}...")')
    print('# print(f"Bulk result: {bulk_result}")')
