#!/usr/bin/env python3
"""
Test the exact same methods that the API endpoints call
"""

import sys
import os

# Add the integrations directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'integrations'))

def test_inmail_method():
    """Test the exact same method call as the API endpoint"""
    print("🧪 Testing InMail method (same as API endpoint)...")
    
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.send_inmail(
            "demilade-adebanjo-554774202",
            "Test Subject",
            "Test message body"
        )

        print(f"Result: {result}")
        
        if result.get("success", False):
            print("✅ InMail method successful!")
        else:
            print("❌ InMail method failed")
            print(f"Error: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

def test_connection_method():
    """Test the exact same method call as the API endpoint"""
    print("\n🧪 Testing Connection method (same as API endpoint)...")
    
    try:
        from linkedin_integration.linkedin_api import LinkedInMessaging
        linkedin = LinkedInMessaging()

        result = linkedin.send_connection_message(
            "demilade-adebanjo-554774202",
            "Hello! I'd like to connect with you on LinkedIn."
        )

        print(f"Result: {result}")
        
        if result.get("success", False):
            print("✅ Connection method successful!")
        else:
            print("❌ Connection method failed")
            print(f"Error: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("Testing API Methods Directly")
    print("=" * 50)
    
    test_inmail_method()
    test_connection_method()
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
